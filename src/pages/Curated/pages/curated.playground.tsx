import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import {
  Zap,
  Shield,
  AlertTriangle,
  Sparkles,
  Wand2,
  Lightbulb
} from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import { PromptEditor, PromptHistory } from '../components'
import { useAppSelector } from '../../../store/hooks'
import { cn } from '../../../utils/cn'

/**
 * Playground Page
 * Admin-only content generation interface
 * Provides prompt editor and generation history
 */
const Playground: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const user = useAppSelector(state => state.auth.user)

  // Check if user has admin privileges
  // Note: This should be replaced with proper role checking from your auth system
  const isAdmin = user?.role === 'admin' || user?.role === 'agent'

  const handleGenerate = useCallback((prompt: string) => {
    // Trigger refresh of prompt history
    setRefreshTrigger(prev => prev + 1)
  }, [])

  // Admin access check
  if (!isAdmin) {
    return (
      <MainLayout
        title="⚡ Playground"
        description="Content generation workspace"
        className="space-y-6"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center justify-center py-20 px-6 text-center"
        >
          <motion.div
            className="w-24 h-24 bg-gradient-to-br from-red-500 to-pink-600 rounded-3xl flex items-center justify-center mb-8 shadow-2xl"
            animate={{ rotate: [0, -5, 5, 0] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            <Shield className="w-12 h-12 text-white" />
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-3xl font-bold text-foreground mb-4"
          >
            Access Restricted
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-muted-foreground max-w-lg mb-8 text-lg leading-relaxed"
          >
            This area is restricted to administrators only. You need admin privileges to access the AI-powered content generation tools.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex items-center gap-3 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-2 border-yellow-200 dark:border-yellow-800 rounded-2xl"
          >
            <div className="p-2 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-600 text-white">
              <AlertTriangle className="w-5 h-5" />
            </div>
            <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Contact your administrator for access
            </span>
          </motion.div>
        </motion.div>
      </MainLayout>
    )
  }

  return (
    <MainLayout
      title="⚡ Playground"
      description="Generate and manage curated content with AI assistance"
      className="space-y-8"
    >
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 p-8 text-white"
      >
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <motion.div
              className="p-3 rounded-2xl bg-white/20 backdrop-blur-sm"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <Wand2 className="w-8 h-8" />
            </motion.div>
            <div>
              <h1 className="text-3xl font-bold mb-2">AI Content Playground</h1>
              <p className="text-blue-100 text-lg">Create engaging educational content with AI assistance</p>
            </div>
          </div>

          {/* Admin indicator */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="flex items-center gap-3 p-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl"
          >
            <div className="p-2 rounded-xl bg-green-500/20">
              <Shield className="w-5 h-5 text-green-300" />
            </div>
            <div>
              <span className="font-semibold text-green-100">Admin Access Granted</span>
              <div className="flex items-center gap-4 mt-1 text-sm text-blue-200">
                <span className="flex items-center gap-1">
                  <Brain className="w-3 h-3" />
                  Content Generation
                </span>
                <span className="flex items-center gap-1">
                  <Target className="w-3 h-3" />
                  Prompt Management
                </span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Floating elements */}
        <motion.div
          className="absolute top-4 right-4 text-white/20"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <Sparkles className="w-12 h-12" />
        </motion.div>
        <motion.div
          className="absolute bottom-4 right-16 text-white/10"
          animate={{ y: [-10, 10, -10] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        >
          <Lightbulb className="w-8 h-8" />
        </motion.div>
      </motion.div>

      {/* Main content - Single page layout */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 h-[calc(100vh-300px)]">
        {/* Prompt Editor - 60% width */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="lg:col-span-3 flex flex-col"
        >
          <PromptEditor
            onGenerate={handleGenerate}
            className="flex-1"
          />
        </motion.div>

        {/* Prompt History - 40% width */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="lg:col-span-2 flex flex-col space-y-6"
        >
          <PromptHistory
            refreshTrigger={refreshTrigger}
            className="flex-1"
          />

          {/* Generation stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-2 border-blue-200/50 dark:border-blue-800/30 rounded-2xl p-6"
          >
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 flex items-center gap-3 mb-6">
              <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                <Zap className="w-4 h-4" />
              </div>
              Quick Stats
            </h4>
            <div className="grid grid-cols-2 gap-6">
              <motion.div
                className="text-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.8, type: "spring" }}
                >
                  {refreshTrigger}
                </motion.div>
                <div className="text-xs text-blue-700 dark:text-blue-300 font-medium">
                  Generated Today
                </div>
              </motion.div>
              <motion.div
                className="text-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mb-1"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.9, type: "spring" }}
                >
                  {user?.full_name?.split(' ')[0] || 'Admin'}
                </motion.div>
                <div className="text-xs text-indigo-700 dark:text-indigo-300 font-medium">
                  Current User
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Footer info */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="mt-8 p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-2xl border-2 border-dashed border-purple-200 dark:border-purple-800"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              className="p-2 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 text-white"
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <Sparkles className="w-5 h-5" />
            </motion.div>
            <div>
              <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                Generated content will be processed and added to the content library
              </span>
              <div className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                AI-powered content creation for educational excellence
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-medium text-purple-700 dark:text-purple-300">
              Last updated
            </div>
            <div className="text-xs text-purple-600 dark:text-purple-400">
              {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>
      </motion.div>
    </MainLayout>
  )
}

export default Playground
