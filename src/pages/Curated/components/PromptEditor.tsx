import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>ap, Loader2, <PERSON>ertCircle, CheckCircle, Trash2 } from 'lucide-react'
import { usePromptGeneration } from '../hooks'
import { cn } from '../../../utils/cn'

export interface PromptEditorProps {
  onGenerate?: (prompt: string) => void
  className?: string
}

/**
 * PromptEditor Component
 * Admin interface for content generation with prompt input and controls
 * Used in Playground page
 */
const PromptEditor: React.FC<PromptEditorProps> = React.memo(({
  onGenerate,
  className
}) => {
  // Default dummy prompt for editing
  const defaultPrompt = `Generate educational questions about Nepali culture and traditions for intermediate level students.

Topics to cover:
- Traditional festivals like Dashain, Tihar, and Holi
- Nepali cuisine and traditional foods
- Cultural practices and customs
- Historical landmarks and monuments
- Traditional clothing and attire

Requirements:
- Create 6 multiple choice questions
- Include both Nepali and English text
- Make questions engaging and educational
- Difficulty level: Intermediate
- Include cultural context and explanations`

  const [prompt, setPrompt] = useState(defaultPrompt)
  const [lastGeneratedPrompt, setLastGeneratedPrompt] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)

  const {
    generating,
    error,
    generateContent,
    clearError
  } = usePromptGeneration()

  const handleGenerate = useCallback(async () => {
    if (!prompt.trim()) return

    const success = await generateContent(prompt)
    
    if (success) {
      setLastGeneratedPrompt(prompt)
      setShowSuccess(true)
      onGenerate?.(prompt)
      
      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000)
    }
  }, [prompt, generateContent, onGenerate])

  const handleClear = useCallback(() => {
    setPrompt('')
    clearError()
  }, [clearError])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleGenerate()
    }
  }, [handleGenerate])

  const promptLength = prompt.length
  const maxLength = 2000
  const isNearLimit = promptLength > maxLength * 0.8

  return (
    <div className={cn(
      'bg-gradient-to-br from-white/90 to-green-50/50 dark:from-gray-900/90 dark:to-green-950/30',
      'border-2 border-green-200/50 dark:border-green-800/30 rounded-2xl p-8 space-y-6',
      'shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm h-full',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-slate-800 dark:text-white">Prompt Editor</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Edit and customize your content generation prompts
            </p>
          </div>
        </div>
        
        {/* Success indicator */}
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex items-center gap-2 text-green-600"
          >
            <CheckCircle className="w-5 h-5" />
            <span className="text-sm font-medium">Generated successfully!</span>
          </motion.div>
        )}
      </div>

      {/* Prompt input */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label htmlFor="prompt" className="text-sm font-medium text-foreground">
            Content Prompt
          </label>
          <div className={cn(
            'text-xs transition-colors duration-200',
            isNearLimit ? 'text-red-500' : 'text-muted-foreground'
          )}>
            {promptLength}/{maxLength}
          </div>
        </div>
        
        <textarea
          id="prompt"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Edit the prompt above or write your own content generation request..."
          maxLength={maxLength}
          className={cn(
            'w-full h-96 px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
            'bg-white/80 dark:bg-gray-900/80 text-foreground placeholder:text-muted-foreground',
            'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
            'hover:border-blue-300/70 dark:hover:border-blue-700/50',
            'transition-all duration-300 ease-out resize-none font-mono text-sm leading-relaxed',
            'shadow-sm hover:shadow-md backdrop-blur-sm',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
          )}
        />
        
        {/* Helper text */}
        <p className="text-xs text-muted-foreground">
          💡 Tip: Use Ctrl+Enter (Cmd+Enter on Mac) to generate quickly
        </p>
      </div>

      {/* Error display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
        </motion.div>
      )}

      {/* Last generated prompt */}
      {lastGeneratedPrompt && lastGeneratedPrompt !== prompt && (
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground mb-1">Last generated:</p>
          <p className="text-sm text-foreground line-clamp-2">{lastGeneratedPrompt}</p>
        </div>
      )}

      {/* Action buttons */}
      <div className="flex items-center gap-3">
        <button
          onClick={handleGenerate}
          disabled={!prompt.trim() || generating}
          className={cn(
            'flex items-center gap-2 px-6 py-3 rounded-lg font-medium',
            'bg-gradient-to-r from-green-500 to-cyan-500 text-white',
            'hover:from-green-600 hover:to-cyan-600 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-green-500/20',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'transform hover:scale-105 active:scale-95'
          )}
        >
          {generating ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Generating...</span>
            </>
          ) : (
            <>
              <Zap className="w-4 h-4" />
              <span>Generate Content</span>
            </>
          )}
        </button>

        <button
          onClick={handleClear}
          disabled={!prompt && !error}
          className={cn(
            'flex items-center gap-2 px-4 py-3 rounded-lg',
            'border border-border text-muted-foreground',
            'hover:text-foreground hover:border-primary/50 transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-primary/20',
            'disabled:opacity-50 disabled:cursor-not-allowed'
          )}
        >
          <Trash2 className="w-4 h-4" />
          <span>Clear</span>
        </button>
      </div>
    </div>
  )
})

PromptEditor.displayName = 'PromptEditor'

export default PromptEditor
